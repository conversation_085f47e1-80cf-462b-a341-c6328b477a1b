export default {
  '/api': {
    target: 'http://localhost:3000',
    changeOrigin: true,
    ws: true
  },
  '/Manage': {
    target: 'http://localhost:8080', // 假设后端服务运行在8080端口
    changeOrigin: true,
    ws: true,
    rewrite: (path: string) => {
      console.log('代理请求:', path);
      return path;
    }
  },
  // 如果有其他API路径，也可以添加
  '/Account': {
    target: 'http://localhost:8080',
    changeOrigin: true,
    ws: true
  }
};
